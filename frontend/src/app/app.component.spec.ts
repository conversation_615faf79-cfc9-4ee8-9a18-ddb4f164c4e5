import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from './shared/mocks/services';
import { AppComponent } from './app.component';

describe('AppComponent', () => {
  beforeEach(
    async () =>
      await TestBed.configureTestingModule({
        imports: [RouterTestingModule],
        declarations: [AppComponent],
        providers: [
          {
            provide: HttpMessagingService,
            useValue: new MockHttpMessagingService(),
          },
        ],
      }).compileComponents(),
  );

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });


});
