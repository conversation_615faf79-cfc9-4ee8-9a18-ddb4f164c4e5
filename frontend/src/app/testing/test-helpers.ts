import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CommonModule, DatePipe } from '@angular/common';
import { RouterTestingModule } from '@angular/router/testing';
import { BrowserAnimationsModule, NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { BriSnackBarModule } from '@davita/bridge-library/snack-bar';
import { BriAlertModule } from '@davita/bridge-library/alert';
import { BriButtonsModule } from '@davita/bridge-library/buttons';
import { BriProgressBarModule } from '@davita/bridge-library/progress-bar';
import { BriTextareaFieldsModule } from '@davita/bridge-library/fields/textarea';
import { BriSubHeaderModule } from '@davita/bridge-library/sub-header';
import { BriChatMessageModule } from '@davita/bridge-library/chat-message';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';

/**
 * Common imports for Angular tests
 */
export const COMMON_TEST_IMPORTS = [
  CommonModule,
  HttpClientTestingModule,
  NoopAnimationsModule,
  MatDividerModule,
  RouterTestingModule,
  BrowserAnimationsModule,
  MatSnackBarModule,
  BriSnackBarModule,
  BriAlertModule,
  BriButtonsModule,
  BriProgressBarModule,
  BriTextareaFieldsModule,
  BriSubHeaderModule,
  BriChatMessageModule,
];

/**
 * Common providers for Angular tests
 */
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';

export const COMMON_TEST_PROVIDERS = [
  DatePipe,
  {
    provide: ActivatedRoute,
    useValue: {
      params: of({ id: '123' }),
      snapshot: { params: { id: '123' } }
    }
  },
  {
    provide: Router,
    useValue: {
      navigate: jasmine.createSpy('navigate'),
      url: '/messaging/landing'
    }
  }
];

/**
 * Common schemas for Angular tests
 */
export const COMMON_TEST_SCHEMAS = [
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
];
