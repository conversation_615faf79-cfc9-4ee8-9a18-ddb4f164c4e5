import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { By } from '@angular/platform-browser';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { UserTypeService } from 'src/app/services/user-type.service';
import { MessagingProxyService } from 'src/app/services/messaging-proxy.service';
import { SnackbarService } from 'src/app/services/snackbar.service';
import { UbiqMessagingComponent } from './ubiq-messaging.component';

describe('UbiqMessagingComponent', () => {
  let component: UbiqMessagingComponent;
  let fixture: ComponentFixture<UbiqMessagingComponent>;

  beforeEach(() => {
    // Create mock for ActivatedRoute
    const mockActivatedRoute = {
      snapshot: {
        paramMap: {
          get: () => null
        }
      }
    };

    // Create mock for Router
    const mockRouter = jasmine.createSpyObj('Router', ['navigate']);

    // Create mock for UserTypeService
    const mockUserTypeService = jasmine.createSpyObj('UserTypeService', ['setUserType']);
    (mockUserTypeService as any).userType$ = of('PATIENT');

    // Create mock for MessagingProxyService
    const mockMessagingProxyService = jasmine.createSpyObj('MessagingProxyService',
      ['setupHttpService', 'getHttpService']);
    mockMessagingProxyService.getHttpService.and.returnValue({
      postNewThreadPlusFirstMessage: jasmine.createSpy('postNewThreadPlusFirstMessage').and.returnValue(of({}))
    });
    // Create mock for SnackbarService
    const mockSnackbarService = jasmine.createSpyObj('SnackbarService',
      ['openSuccessToast', 'openErrorToast']);

    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        HttpClientTestingModule,
        BrowserAnimationsModule
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Router, useValue: mockRouter },
        { provide: UserTypeService, useValue: mockUserTypeService },
        { provide: MessagingProxyService, useValue: mockMessagingProxyService },
        { provide: SnackbarService, useValue: mockSnackbarService }
      ]
    });

    fixture = TestBed.createComponent(UbiqMessagingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });



  it('should have app-new-message component', () => {
    const newMessageComponent = fixture.debugElement.query(By.css('app-new-message'));
    expect(newMessageComponent).toBeTruthy();
  });

  it('should set isWidget to true on app-new-message', () => {
    const newMessageComponent = fixture.debugElement.query(By.css('app-new-message'));
    // Check the input binding directly from the component template
    expect(newMessageComponent.attributes['ng-reflect-is-widget']).toBe('true');
  });

  it('should emit closeMessagingPanel when closeMessage is called', () => {
    // Spy on the output event
    spyOn(component.closeMessagingPanel, 'emit');

    // Call the closeMessage method
    component.closeMessage();

    // Verify the emit method was called
    expect(component.closeMessagingPanel.emit).toHaveBeenCalled();
  });

  it('should handle closeMessagingPanel event from app-new-message', () => {
    // Spy on the closeMessage method
    spyOn(component, 'closeMessage');

    // Get the app-new-message component
    const newMessageComponent = fixture.debugElement.query(By.css('app-new-message'));

    // Trigger the closeMessagingPanel event
    newMessageComponent.triggerEventHandler('closeMessagingPanel', null);

    // Verify the closeMessage method was called
    expect(component.closeMessage).toHaveBeenCalled();
  });
});
