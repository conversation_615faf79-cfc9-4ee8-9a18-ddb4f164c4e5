import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService, MockBriPatientAuthorizationService } from '../shared/mocks/services';
import { MessagingComponent } from './messaging.component';
import { mockThreads } from '../shared/mocks/data';
import { Router, NavigationEnd } from '@angular/router';
import { DatePipe, Location } from '@angular/common';
import { WebSocketService } from '../services/web-socket.service';
import { MessagingStateService } from '../services/messaging-state.service';
import { DeviceStateService } from '../services/device-state.service';
import { UserService } from '../services/user.service';
import { ThemeSwitchService } from '@davita/bridge-library/theme-switch';
import { BriPatientAuthorizationService } from '@davita/bridge-utility-library/authorization';
import { of, Subject } from 'rxjs';
import { Status } from 'src/app/models';
import { MessagingSenderUserType } from 'src/app/models/roles';
import { By } from '@angular/platform-browser';
import { COMMON_TEST_IMPORTS, COMMON_TEST_PROVIDERS, COMMON_TEST_SCHEMAS } from 'src/app/testing/test-helpers';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('MessagingComponent', () => {
  let component: MessagingComponent;
  let fixture: ComponentFixture<MessagingComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockLocation: jasmine.SpyObj<Location>;
  let mockWebSocketService: jasmine.SpyObj<WebSocketService>;
  let mockMessagingStateService: jasmine.SpyObj<MessagingStateService>;
  let mockDeviceStateService: jasmine.SpyObj<DeviceStateService>;
  let mockUserService: jasmine.SpyObj<UserService>;

  beforeEach(async () => {
    mockRouter = jasmine.createSpyObj('Router', ['navigate', 'events'], {
      url: '/messaging/landing',
      events: of(new NavigationEnd(1, '/messaging/thread/123', '/messaging/thread/123'))
    });

    mockLocation = jasmine.createSpyObj('Location', ['back']);
    mockWebSocketService = jasmine.createSpyObj('WebSocketService', ['initWebSocket']);
    mockMessagingStateService = jasmine.createSpyObj('MessagingStateService',
      ['updateThreads', 'updateThreadUnreadMessageCount', 'setUser']);
    // Set up the threads$ property as a getter to return the observable
    Object.defineProperty(mockMessagingStateService, 'threads$', {
      get: () => of(mockThreads)
    });

    mockDeviceStateService = jasmine.createSpyObj('DeviceStateService', ['determineDevice']);
    mockDeviceStateService.determineDevice.and.returnValue('desktop');
    mockDeviceStateService.threadListVisible = true;

    mockUserService = jasmine.createSpyObj('UserService', ['createUserIfNotExists']);
    // Create a mock user that matches the User interface
    const mockUser = {
      externalId: '123',
      isPractitioner: false,
      firstName: 'Test',
      lastName: 'User',
      id: '123',
      cernerId: '12345',
      role: 'patient',
      adUsername: 'testuser',
      userType: MessagingSenderUserType.patient,
      mpi: '12345'
    };
    mockUserService.createUserIfNotExists.and.returnValue(of(mockUser));

    await TestBed.configureTestingModule({
      imports: [
        ...COMMON_TEST_IMPORTS,
        HttpClientTestingModule
      ],
      declarations: [MessagingComponent],
      providers: [
        ...COMMON_TEST_PROVIDERS,
        { provide: Router, useValue: mockRouter },
        { provide: Location, useValue: mockLocation },
        { provide: WebSocketService, useValue: mockWebSocketService },
        { provide: MessagingStateService, useValue: mockMessagingStateService },
        { provide: DeviceStateService, useValue: mockDeviceStateService },
        { provide: UserService, useValue: mockUserService },
        { provide: ThemeSwitchService, useValue: jasmine.createSpyObj('ThemeSwitchService', ['switch']) },
        { provide: BriPatientAuthorizationService, useClass: MockBriPatientAuthorizationService },
        { provide: HttpMessagingService, useValue: new MockHttpMessagingService() },
      ],
      schemas: COMMON_TEST_SCHEMAS
    }).compileComponents();

    // Create the component but don't call detectChanges() to avoid rendering the template
    fixture = TestBed.createComponent(MessagingComponent);
    component = fixture.componentInstance;

    // Manually set the threads property instead of relying on the template rendering
    component.threads = mockThreads;
  });

  afterEach(() => {
    fixture.destroy();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain thread messages', () => {
    expect(component.threads).toEqual(mockThreads);
  });



  it('should initialize websocket if not on native platform', () => {
    // Manually call ngOnInit to initialize the component
    component.ngOnInit();
    expect(mockWebSocketService.initWebSocket).toHaveBeenCalled();
  });

  it('should call goBack when back button is clicked', () => {
    spyOn(component, 'goBack').and.callThrough();
    // Directly call the method since we can't easily trigger the backBtnClicked event
    component.goBack();
    expect(component.goBack).toHaveBeenCalled();
    expect(mockLocation.back).toHaveBeenCalled();
  });

  it('should navigate to new message when newMessage is called', () => {
    component.newMessage();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/messaging/new'], { skipLocationChange: true });
    expect(mockDeviceStateService.threadListVisible).toBeFalse();
  });

  it('should navigate to thread when threadClick is called', () => {
    const thread = mockThreads[0];
    component.threadClick(thread);
    expect(mockRouter.navigate).toHaveBeenCalledWith([`/messaging/thread/${thread.id}`], { skipLocationChange: true });
    expect(mockDeviceStateService.threadListVisible).toBeFalse();
  });

  it('should update unread messages when threadClick is called with unread messages', () => {
    // Create a thread with unread messages
    const thread = { ...mockThreads[0], unreadMessages: 5 };

    // Create a mock HttpMessagingService
    const mockHttpMessagingService = jasmine.createSpyObj('HttpMessagingService', ['updateThreadUnreadMessages']);
    mockHttpMessagingService.updateThreadUnreadMessages.and.returnValue(of(0));

    // Set the mock service on the component
    (component as any).httpMessagingService = mockHttpMessagingService;

    // Call the method
    component.threadClick(thread);

    // Verify the HTTP service was called
    expect(mockHttpMessagingService.updateThreadUnreadMessages).toHaveBeenCalledWith(thread.id);

    // Verify the messaging state service was called
    expect(mockMessagingStateService.updateThreadUnreadMessageCount).toHaveBeenCalledWith(thread.id, 0);
  });

  it('should format date correctly for today', () => {
    // Create a date for today
    const today = new Date();
    const todayString = today.toISOString();

    // Create a mock DatePipe
    const mockDatePipe = jasmine.createSpyObj('DatePipe', ['transform']);
    mockDatePipe.transform.and.returnValue('3:30 PM');

    // Set the mock DatePipe on the component
    component['datePipe'] = mockDatePipe;

    // Format the date
    const formattedDate = component.dateFormat(todayString);

    // Verify the DatePipe was called with the correct format
    expect(mockDatePipe.transform).toHaveBeenCalledWith(jasmine.any(Date), 'h:mm a');
    expect(formattedDate).toBe('3:30 PM');
  });

  it('should format date correctly for other days', () => {
    // Create a date for yesterday
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayString = yesterday.toISOString();

    // Create a mock DatePipe
    const mockDatePipe = jasmine.createSpyObj('DatePipe', ['transform']);
    mockDatePipe.transform.and.returnValue('04/16');

    // Set the mock DatePipe on the component
    component['datePipe'] = mockDatePipe;

    // Format the date
    const formattedDate = component.dateFormat(yesterdayString);

    // Verify the DatePipe was called with the correct format
    expect(mockDatePipe.transform).toHaveBeenCalledWith(jasmine.any(Date), 'MM/dd');
    expect(formattedDate).toBe('04/16');
  });

  it('should update deviceType on resize', () => {
    // Set up the mock to return a different device type
    mockDeviceStateService.determineDevice.and.returnValue('mobile');

    // Trigger the resize event
    component.onResize();

    // Verify the device type was updated
    expect(component.deviceType).toBe('mobile');
    expect(mockDeviceStateService.determineDevice).toHaveBeenCalled();
  });



  it('should unsubscribe from subscriptions on destroy', () => {
    // Create spies for the subscriptions
    const routerSubscriptionSpy = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    const messagingSubscriptionSpy = jasmine.createSpyObj('Subscription', ['unsubscribe']);

    // Set the spies on the component
    component['routerSubscription'] = routerSubscriptionSpy;
    component['messagingSubscription'] = messagingSubscriptionSpy;

    // Destroy the component
    component.ngOnDestroy();

    // Verify the subscriptions were unsubscribed
    expect(routerSubscriptionSpy.unsubscribe).toHaveBeenCalled();
    expect(messagingSubscriptionSpy.unsubscribe).toHaveBeenCalled();
  });
});
