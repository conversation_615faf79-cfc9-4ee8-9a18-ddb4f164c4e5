<div
  [ngClass]="{ 'messaging-body-mobile': deviceType !== 'desktop' }"
  class="messaging-body"
>
  <div
    id="messaging-main-content"
    class="main-content"
    [ngClass]="
      deviceState.threadListVisible ? 'legal-padding' : 'footer-padding'
    "
  >
    <div [ngClass]="{ 'sub-header-container': deviceType === 'desktop' }">
      <bri-sub-header-v2
        [sectionHeader]="'Messages'"
        [leftContent]="{ showBackButton: true, backText: 'BACK' }"
        (backBtnClicked)="goBack()"
        [hideBackIcon]="false"
        [placeholder]="''"
        [rightContent]="
          deviceType !== 'desktop' && messageLoadStatus !== Status.error
            ? [{ name: 'di di-document44', text: 'New Message' }]
            : undefined
        "
        (rightButtonClicked)="newMessage()"
      />
      <div
        class="new-message-button"
        *ngIf="deviceType === 'desktop' && messageLoadStatus !== Status.error"
      >
        <bri-buttons
          briFlat
          [btnText]="'New Message'"
          [iconName]="'di di-document44'"
          (buttonClicked)="newMessage()"
        />
      </div>
    </div>
    <ng-container *ngIf="messageLoadStatus === Status.success; else loadingApp">
      <div class="content">
        <div
          class="local-message-list"
          *ngIf="
            (deviceType === 'desktop' && threads.length > 0) ||
            (threads.length > 0 && deviceState.threadListVisible)
          "
        >
          <div class="threadsContainer">
            <div class="scroll-container" #scrollContainer>
              <ng-container *ngIf="threadsLoaded; else loadingThreads">
                <ng-container *ngFor="let thread of threads">
                  <div
                    id="thread-element"
                    (click)="threadClick(thread)"
                    [ngClass]="
                      thread.id === this.activeThread
                        ? 'selectedThread'
                        : 'threadStyle'
                    "
                  >
                    <bri-master-list-item
                      class="thread-message-list-item"
                      [id]="thread.id"
                      type="icon"
                      [textType]="'line2'"
                      [textList]="[
                        { text: thread.subject, className: 'mat-subtitle-1' },
                        { text: thread.lastMessage, className: 'mat-body-2' },
                      ]"
                      [rightText]="dateFormat(thread.lastMessageSentAt)"
                      [badge]="thread.unreadMessages > 0"
                      [badgeNumber]="thread.unreadMessages"
                      [overlapOff]="true"
                    />
                  </div>
                  <mat-divider />
                </ng-container>
              </ng-container>
              <ng-template #loadingThreads>
                <div class="loading">
                  <bri-progress-bar
                    message="Getting Messages"
                  ></bri-progress-bar>
                </div>
              </ng-template>
            </div>
          </div>
          <div class="legal-text">
            <mat-divider />
            <p class="mat-caption">
              In case of emergency please dial 911. If you need an immediate
              response, please contact the clinician directly.
            </p>
          </div>
        </div>
        <div
          [ngClass]="{
            'chat-space': deviceType === 'desktop' && threads.length > 0,
            'chat-space-singular':
              deviceType === 'desktop' && threads.length <= 0,
            'chat-space-mobile': deviceType !== 'desktop',
          }"
          *ngIf="
            deviceType === 'desktop' ||
            !deviceState.threadListVisible ||
            threads.length === 0
          "
        >
          <router-outlet></router-outlet>
        </div>
      </div>
    </ng-container>
    <ng-template #loadingApp>
      <ng-container
        *ngIf="messageLoadStatus === Status.loading; else ErrorLoading"
      >
        <div class="loading">
          <bri-progress-bar message="Getting Messages"></bri-progress-bar>
        </div>
      </ng-container>
      <ng-template #ErrorLoading>
        <div class="error">
          <bri-empty-state
            [isCircleBorder]="false"
            [isEmptyStateSVG]="false"
            [iconClass]="'di di-alert'"
            [headerText]="'Error'"
            [messageText]="errorDescription"
          ></bri-empty-state>
        </div>
      </ng-template>
    </ng-template>
  </div>
</div>
