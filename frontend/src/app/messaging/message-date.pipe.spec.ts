import { DatePipe } from '@angular/common';
import { MessageDatePipe } from '../shared/message-date.pipe';
import { TestBed } from '@angular/core/testing';
import { COMMON_TEST_IMPORTS, COMMON_TEST_PROVIDERS } from '../testing/test-helpers';

describe('MessageDatePipe', () => {
  let pipe: MessageDatePipe;
  let datePipe: DatePipe;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: COMMON_TEST_IMPORTS,
      providers: COMMON_TEST_PROVIDERS
    });

    datePipe = TestBed.inject(DatePipe);
    pipe = new MessageDatePipe(datePipe);
  });

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return "Today" for today\'s date', () => {
    const today = new Date();
    const result = pipe.transform(today.toISOString());
    expect(result).toBe('Today');
  });

  it('should return "Yesterday" for yesterday\'s date', () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const result = pipe.transform(yesterday.toISOString());
    expect(result).toBe('Yesterday');
  });

  it('should return day of week for dates within the last week', () => {
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

    // Mock the datePipe.transform to return a specific day
    spyOn(datePipe, 'transform').and.returnValue('Monday');

    const result = pipe.transform(threeDaysAgo.toISOString());
    expect(result).toBe('Monday');
  });

  it('should return MM/dd format for dates older than a week', () => {
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

    // Mock the datePipe.transform to return a specific date format
    spyOn(datePipe, 'transform').and.returnValue('05/15');

    const result = pipe.transform(twoWeeksAgo.toISOString());
    expect(result).toBe('05/15');
  });

  it('should handle normalizeDate correctly', () => {
    const date = new Date(2023, 5, 15, 13, 45, 30); // June 15, 2023, 13:45:30
    const normalized = pipe.normalizeDate(date);

    expect(normalized.getHours()).toBe(0);
    expect(normalized.getMinutes()).toBe(0);
    expect(normalized.getSeconds()).toBe(0);
    expect(normalized.getMilliseconds()).toBe(0);
    expect(normalized.getDate()).toBe(15);
    expect(normalized.getMonth()).toBe(5); // June
    expect(normalized.getFullYear()).toBe(2023);
  });
});
