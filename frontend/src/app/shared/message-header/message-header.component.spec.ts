import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from 'src/app/shared/mocks/services';
import { MessageHeaderComponent } from './message-header.component';
import { BriAlertComponent } from '@davita/bridge-library/alert';
import { BriTextFieldsModule } from '@davita/bridge-library/fields/text';
import { By } from '@angular/platform-browser';
import { FormControl } from '@angular/forms';
import { DeviceStateService } from 'src/app/services/device-state.service';
import { CapcitorAppRefreshService } from '@davita/bridge-library/capacitor-app-refresh';
import { COMMON_TEST_IMPORTS, COMMON_TEST_PROVIDERS, COMMON_TEST_SCHEMAS } from 'src/app/testing/test-helpers';

describe('MessageHeaderComponent', () => {
  let component: MessageHeaderComponent;
  let fixture: ComponentFixture<MessageHeaderComponent>;

  let mockDeviceStateService: jasmine.SpyObj<DeviceStateService>;
  let mockCapcitorAppRefreshService: jasmine.SpyObj<CapcitorAppRefreshService>;
  let mockAlertComponent: jasmine.SpyObj<BriAlertComponent>;

  beforeEach(async () => {
    mockDeviceStateService = jasmine.createSpyObj('DeviceStateService', ['determineDevice']);
    mockDeviceStateService.determineDevice.and.returnValue('desktop');
    mockDeviceStateService.threadListVisible = false;

    mockCapcitorAppRefreshService = jasmine.createSpyObj('CapcitorAppRefreshService', ['setlockCapacitorAppRefresh']);
    mockAlertComponent = jasmine.createSpyObj('BriAlertComponent', ['open', 'close']);

    await TestBed.configureTestingModule({
      imports: [
        ...COMMON_TEST_IMPORTS,
        BriTextFieldsModule,
      ],
      declarations: [MessageHeaderComponent],
      providers: [
        ...COMMON_TEST_PROVIDERS,
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        {
          provide: DeviceStateService,
          useValue: mockDeviceStateService
        },
        {
          provide: CapcitorAppRefreshService,
          useValue: mockCapcitorAppRefreshService
        }
      ],
      schemas: COMMON_TEST_SCHEMAS
    }).compileComponents();
    fixture = TestBed.createComponent(MessageHeaderComponent);
    component = fixture.componentInstance;

    // Set up test data
    component.subjectFormControl = new FormControl('Test Subject');
    component.subject = 'Test Thread';
    component.hasUserInputedText = true;

    // Set up the spy for the alert component
    component['component'] = mockAlertComponent;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain Subject:', () => {
    const compiled = fixture.debugElement.nativeElement;
    const inputTitle = compiled.querySelector('.mat-subtitle-1');
    expect(inputTitle.textContent).toContain('Subject:');
  });

  it('contains input box for subject', () => {
    const inputElement =
      fixture.debugElement.nativeElement.querySelector('#subject');
    expect(inputElement).toBeTruthy();
  });

  it('contains cancel button', () => {
    const buttons = fixture.debugElement.queryAll(By.css('button'));
    const cancelButton = Array.from(buttons).find(
      (button) => button.nativeElement.textContent?.trim() === 'Cancel',
    );
    expect(cancelButton).toBeTruthy();
  });

  it('should contain New Message heading', () => {
    // Set component to new message mode
    component.readOnly = false;
    component.subject = '';
    fixture.detectChanges();

    const compiled = fixture.debugElement.nativeElement;
    const inputTitle = compiled.querySelector('.message-title-container');
    expect(inputTitle.textContent).toContain('New Message');
  });

  it('modal appears when user clicks cancel button', () => {
    const buttons = fixture.debugElement.queryAll(By.css('button'));
    const cancelButton = Array.from(buttons).find(
      (button) => button.nativeElement.textContent?.trim() === 'Cancel',
    );

    cancelButton?.triggerEventHandler('click', null);

    fixture.detectChanges();
    const modalElement = document.querySelector('.cdk-overlay-container');
    expect(modalElement).toBeTruthy();
    expect(modalElement?.textContent).toContain('Cancel your new message?');
  });

  it('cancel modal contains correct headline', () => {
    const buttons = fixture.debugElement.queryAll(By.css('button'));
    const cancelButton = Array.from(buttons).find(
      (button) => button.nativeElement.textContent?.trim() === 'Cancel',
    );

    cancelButton?.triggerEventHandler('click', null);

    fixture.detectChanges();
    const modalElement = document.querySelector('.cdk-overlay-container');
    expect(modalElement?.textContent).toContain('Cancel your new message?');
  });

  it('should emit newMessageClosed event when redirectUserToLandingPage is called', () => {
    // Create a mock BriAlertComponent
    const mockAlert = jasmine.createSpyObj('BriAlertComponent', ['close']);
    component['component'] = mockAlert;

    // Set isAlertOpen to true to trigger the close call
    (component as any).isAlertOpen = true;

    // Spy on the emit method
    spyOn(component.newMessageClosed, 'emit');

    // Call the method
    component.redirectUserToLandingPage();

    // Verify the event was emitted
    expect(component.newMessageClosed.emit).toHaveBeenCalled();

    // Verify the thread list is visible
    expect(mockDeviceStateService.threadListVisible).toBeTrue();

    // Verify the modal was closed
    expect(mockAlert.close).toHaveBeenCalled();

    // Verify the capacitor app refresh was unlocked
    expect(mockCapcitorAppRefreshService.setlockCapacitorAppRefresh).toHaveBeenCalledWith(false);
  });

  it('should open cancel modal with correct data', () => {
    // Create a mock BriAlertComponent
    const mockAlert = jasmine.createSpyObj('BriAlertComponent', ['open']);
    component['component'] = mockAlert;

    // Call the method
    component.openCancelModal();

    // Verify the modal was opened with the correct data
    expect(mockAlert.open).toHaveBeenCalledWith({
      header: 'Cancel your new message?',
      description: 'Are you sure you want to cancel? Your unsent message will not send or be saved.',
      primaryButtonLabel: 'Yes, Cancel',
      secondaryButtonLabel: 'No',
    });
  });

  it('should close modal when closeModal is called', () => {
    // Create a mock BriAlertComponent
    const mockAlert = jasmine.createSpyObj('BriAlertComponent', ['close']);
    component['component'] = mockAlert;

    // Call the method
    component.closeModal();

    // Verify the modal was closed
    expect(mockAlert.close).toHaveBeenCalled();
  });

  it('should update deviceType on resize', () => {
    mockDeviceStateService.determineDevice.and.returnValue('mobile');
    component.onResize();
    expect(component.deviceType).toBe('mobile');
    expect(mockDeviceStateService.determineDevice).toHaveBeenCalled();
  });
});
