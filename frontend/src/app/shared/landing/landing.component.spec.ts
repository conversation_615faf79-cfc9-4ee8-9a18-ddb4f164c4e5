import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService, MockDeviceStateService, MockMessagingStateService } from '../mocks/services';
import { LandingComponent } from './landing.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { BriPatientAuthorizationService } from '@davita/bridge-utility-library/authorization';
import { MockBriPatientAuthorizationService } from '../mocks/services';
import { DeviceStateService } from 'src/app/services/device-state.service';
import { MessagingStateService } from 'src/app/services/messaging-state.service';
import { BehaviorSubject, of } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA, Component, Input } from '@angular/core';
import { mockThreads } from '../mocks/data';

describe('LandingComponent', () => {
  let component: LandingComponent;
  let fixture: ComponentFixture<LandingComponent>;

  let mockDeviceStateService: MockDeviceStateService;
  let mockMessagingStateService: MockMessagingStateService;
  let threadsSubject: BehaviorSubject<any[]>;

  // Create a mock BriEmptyState component
  @Component({
    selector: 'bri-empty-state',
    template: '<div class="mock-empty-state">{{ headerText }} {{ messageText }}</div>'
  })
  class MockBriEmptyStateComponent {
    @Input() isCircleBorder: boolean = false;
    @Input() isEmptyStateSVG: boolean = false;
    @Input() iconClass: string = '';
    @Input() headerText: string = '';
    @Input() messageText: string = '';
  }

  beforeEach(async () => {
    mockDeviceStateService = new MockDeviceStateService();
    mockMessagingStateService = new MockMessagingStateService();

    // Create a subject we can control for testing
    threadsSubject = new BehaviorSubject<any[]>([]);
    mockMessagingStateService.threads$ = threadsSubject.asObservable();

    await TestBed.configureTestingModule({
      declarations: [LandingComponent, MockBriEmptyStateComponent],
      providers: [
        {
          provide: BriPatientAuthorizationService,
          useClass: MockBriPatientAuthorizationService,
        },
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        {
          provide: DeviceStateService,
          useValue: mockDeviceStateService
        },
        {
          provide: MessagingStateService,
          useValue: mockMessagingStateService
        },
        HttpClient,
        HttpHandler,
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    fixture = TestBed.createComponent(LandingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain welcome message when threads are empty', () => {
    // Set threads to empty
    threadsSubject.next([]);
    fixture.detectChanges();

    // Get the empty state component
    const emptyStateElement = fixture.debugElement.query(By.css('.mock-empty-state'));
    expect(emptyStateElement).toBeTruthy();
    expect(emptyStateElement.nativeElement.textContent).toContain('Welcome to Messages, a place to connect with your care team!');
  });

  it('should contain different message when threads exist', () => {
    // Set threads to non-empty
    threadsSubject.next(mockThreads);
    fixture.detectChanges();

    // Get the empty state component
    const emptyStateElement = fixture.debugElement.query(By.css('.mock-empty-state'));
    expect(emptyStateElement).toBeTruthy();
    expect(emptyStateElement.nativeElement.textContent).toContain('Connect with your care team here.');
    expect(emptyStateElement.nativeElement.textContent).toContain('Select "New Message" or an existing message thread.');
  });

  it('should update deviceType on resize', () => {
    spyOn(mockDeviceStateService, 'determineDevice').and.returnValue('mobile');
    component.onResize();
    expect(component.deviceType).toBe('mobile');
    expect(mockDeviceStateService.determineDevice).toHaveBeenCalled();
  });

  it('should set threadListVisible to true on init', () => {
    expect(mockDeviceStateService.threadListVisible).toBeTrue();
  });
});
