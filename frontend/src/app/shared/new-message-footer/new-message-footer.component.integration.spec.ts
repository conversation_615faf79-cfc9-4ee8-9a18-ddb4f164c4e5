import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NewMessageFooterComponent } from './new-message-footer.component';
import { CameraService } from 'src/app/services/camera.service';
import { SnackbarService } from 'src/app/services/snackbar.service';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { BriButtonsModule } from '@davita/bridge-library/buttons';
import { BriAlertModule } from '@davita/bridge-library/alert';
import { BriTextareaFieldsModule } from '@davita/bridge-library/fields/textarea';
import { MatDividerModule } from '@angular/material/divider';
import { BriSnackBarModule } from '@davita/bridge-library/snack-bar';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

// Enhanced Mock CameraService for integration testing
class MockCameraServiceIntegration {
  private isNative = false;

  setNativePlatform(isNative: boolean) {
    this.isNative = isNative;
  }

  isNativePlatform(): boolean {
    return this.isNative;
  }

  async selectPhoto() {
    if (this.isNative) {
      // Simulate successful photo selection on mobile
      return {
        webPath: 'mock-path',
        format: 'jpeg',
        saved: false,
        file: new File(['mock-image-data'], 'test-photo.jpg', { type: 'image/jpeg' })
      };
    }
    return null;
  }

  async checkPermissions(): Promise<boolean> {
    return this.isNative;
  }

  async requestPermissions(): Promise<boolean> {
    return this.isNative;
  }
}

// Mock SnackbarService
class MockSnackbarService {
  openErrorToast(message: string) {
    console.log('Error toast:', message);
  }
}

// Mock HttpMessagingService
class MockHttpMessagingService {
  // Add any required methods here
}

describe('NewMessageFooterComponent Integration Tests', () => {
  let component: NewMessageFooterComponent;
  let fixture: ComponentFixture<NewMessageFooterComponent>;
  let mockCameraService: MockCameraServiceIntegration;
  let mockSnackbarService: MockSnackbarService;

  beforeEach(async () => {
    mockCameraService = new MockCameraServiceIntegration();
    mockSnackbarService = new MockSnackbarService();

    await TestBed.configureTestingModule({
      imports: [
        BriButtonsModule,
        MatDividerModule,
        BriAlertModule,
        BriTextareaFieldsModule,
        NoopAnimationsModule,
        BriSnackBarModule,
        MatSnackBarModule,
      ],
      declarations: [NewMessageFooterComponent],
      providers: [
        {
          provide: CameraService,
          useValue: mockCameraService,
        },
        {
          provide: SnackbarService,
          useValue: mockSnackbarService,
        },
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(NewMessageFooterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should handle mobile photo selection correctly', async () => {
    // Simulate mobile platform
    mockCameraService.setNativePlatform(true);
    
    // Spy on the addImageToList method
    spyOn(component as any, 'addImageToList').and.callThrough();
    
    // Trigger photo button click
    await component.onPhotoButtonClick();
    
    // Verify that addImageToList was called with a file
    expect((component as any).addImageToList).toHaveBeenCalled();
  });

  it('should fall back to file input on web platform', async () => {
    // Simulate web platform
    mockCameraService.setNativePlatform(false);
    
    // Mock document.querySelector to return a mock file input
    const mockFileInput = {
      click: jasmine.createSpy('click')
    };
    spyOn(document, 'querySelector').and.returnValue(mockFileInput as any);
    
    // Trigger photo button click
    await component.onPhotoButtonClick();
    
    // Verify that file input click was called
    expect(mockFileInput.click).toHaveBeenCalled();
  });

  it('should handle permission denial gracefully', async () => {
    // Simulate mobile platform with denied permissions
    mockCameraService.setNativePlatform(true);
    spyOn(mockCameraService, 'checkPermissions').and.returnValue(Promise.resolve(false));
    spyOn(mockCameraService, 'requestPermissions').and.returnValue(Promise.resolve(false));
    spyOn(mockSnackbarService, 'openErrorToast');
    
    // Trigger photo button click
    await component.onPhotoButtonClick();
    
    // Verify error message was shown
    expect(mockSnackbarService.openErrorToast).toHaveBeenCalledWith(
      'Camera permissions are required to take photos'
    );
  });

  it('should validate file types correctly', async () => {
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    spyOn(mockSnackbarService, 'openErrorToast');
    
    // Call the private addImageToList method
    await (component as any).addImageToList(invalidFile);
    
    // Verify error message for invalid file type
    expect(mockSnackbarService.openErrorToast).toHaveBeenCalledWith(
      'File of type text/plain is not allowed.'
    );
  });

  it('should enforce image upload limit', async () => {
    // Fill up the image list to the limit
    const maxImages = 3; // IMAGE_UPLOAD_LIMIT
    for (let i = 0; i < maxImages; i++) {
      component.userSubmittedImages.push({
        imageFile: new File(['test'], `test${i}.jpg`, { type: 'image/jpeg' }),
        fileName: `test${i}.jpg`
      });
    }
    
    const additionalFile = new File(['test'], 'extra.jpg', { type: 'image/jpeg' });
    spyOn(mockSnackbarService, 'openErrorToast');
    
    // Try to add one more image
    await (component as any).addImageToList(additionalFile);
    
    // Verify error message for exceeding limit
    expect(mockSnackbarService.openErrorToast).toHaveBeenCalledWith(
      'You can only submit up to 3 images per message'
    );
  });
});
