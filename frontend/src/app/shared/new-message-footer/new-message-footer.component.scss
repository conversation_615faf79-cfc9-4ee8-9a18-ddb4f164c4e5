@import "@davita/bridge-library/scss/colors";

.input-container {
  width: 100%;
}

.typing-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  padding: 16px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom, 0));
  gap: 8px;
  overflow: hidden;
}

.images-container {
  width: 100%;
  border: $light-color-ui-grey solid 1px; /* Adjust the width as needed */
  border-bottom: none;
  border-top: none;
  padding: 5px 15px;
}

.images-container:first-of-type {
  border-top: 1px solid $light-color-ui-grey;
  padding-top: 10px;
  border-top-right-radius: 2px;
  border-top-left-radius: 2px;
}

.image-divider,
.image-divider-no-images {
  display: flex;
  justify-content: center;
  padding: 0 16px;
  border-bottom: none;
  mat-divider {
    width: 100%;
    border: 1px solid $light-color-ui-separator;
    margin-top: 5px;
    @media (prefers-color-scheme: dark) {
      border: 1px solid $dark-color-ui-separator;
    }
  }
}

.image-divider {
  border-left: 1px solid $light-color-ui-grey;
  border-right: 1px solid $light-color-ui-grey;
}

.image-divider-no-images {
  border-top-right-radius: 2px;
  border-top-left-radius: 2px;
}

bri-textarea-fields ::ng-deep .mdc-text-field--outlined {
  --mdc-outlined-text-field-outline-width: 0px !important;
}

bri-textarea-fields.textarea-with-image ::ng-deep mat-form-field .mdc-text-field--outlined{
  border: 1px solid $light-color-ui-grey !important;
  border-bottom-left-radius: 2px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 2px;
  border-top: none !important;
}

bri-textarea-fields.textarea-without-image ::ng-deep mat-form-field .mdc-text-field--outlined{
  border: 1px solid $light-color-ui-grey !important;
  border-radius: 2px;
}

mat-divider {
  background-color: $light-color-ui-separator;
  border-top: 1px solid $light-color-ui-separator;
  @media (prefers-color-scheme: dark) {
    background-color: $dark-color-ui-separator;
    border-top: 1px solid $dark-color-ui-separator;
  }
}

bri-textarea-fields
  ::ng-deep
  div
  div
  mat-form-field
  div
  div
  div
  .mat-subtitle-1::placeholder {
  color: $light-color-text-50 !important;
}

.input-box-area {
  width: 100%;
  max-width: 60vw;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

@media screen and (max-width: 1023px) {
  .input-box-area {
    max-width: 35vw;
  }
}

@media screen and (max-width: 959px) {
  .input-box-area {
    max-width: 80vw;
  }
}

@media screen and (max-width: 599px) {
  .input-box-area {
    max-width: 70vw;
  }
}

@media screen and (max-width: 449px) {
  .input-box-area {
    max-width: 62vw;
  }
}
