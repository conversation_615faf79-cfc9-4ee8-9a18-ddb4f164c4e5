import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService, MockCapcitorAppRefreshService } from 'src/app/shared/mocks/services';
import { NewMessageFooterComponent } from './new-message-footer.component';
import { BriAlertComponent } from '@davita/bridge-library/alert';
import { FormControl } from '@angular/forms';
import { DeviceStateService } from 'src/app/services/device-state.service';
import { SnackbarService } from 'src/app/services/snackbar.service';
import { ColorHexService } from '@davita/bridge-library/color-hex.service';
import { DarkModeService } from '@davita/bridge-library/dark-mode.service';
import { CapcitorAppRefreshService } from '@davita/bridge-library/capacitor-app-refresh';
import { of } from 'rxjs';
import { COMMON_TEST_IMPORTS, COMMON_TEST_PROVIDERS, COMMON_TEST_SCHEMAS } from 'src/app/testing/test-helpers';
import { CameraService } from 'src/app/services/camera.service';

// Mock CameraService
class MockCameraService {
  isNativePlatform(): boolean {
    return false;
  }

  async selectPhoto() {
    return {
      webPath: 'mock-path',
      format: 'jpeg',
      saved: false,
      file: new File(['mock'], 'mock.jpg', { type: 'image/jpeg' })
    };
  }

  async checkPermissions(): Promise<boolean> {
    return true;
  }

  async requestPermissions(): Promise<boolean> {
    return true;
  }
}

describe('NewMessageFooterComponent', () => {
  let component: NewMessageFooterComponent;
  let fixture: ComponentFixture<NewMessageFooterComponent>;

  let mockDeviceStateService: jasmine.SpyObj<DeviceStateService>;
  let mockSnackbarService: jasmine.SpyObj<SnackbarService>;
  let mockColorHexService: jasmine.SpyObj<ColorHexService>;
  let mockDarkModeService: jasmine.SpyObj<DarkModeService>;
  let mockCapcitorAppRefreshService: MockCapcitorAppRefreshService;
  let mockAlertComponent: jasmine.SpyObj<BriAlertComponent>;

  beforeEach(async () => {
    mockDeviceStateService = jasmine.createSpyObj('DeviceStateService', ['determineDevice']);
    mockDeviceStateService.determineDevice.and.returnValue('desktop');

    mockSnackbarService = jasmine.createSpyObj('SnackbarService', ['openErrorToast']);
    mockColorHexService = jasmine.createSpyObj('ColorHexService', ['getColorHex']);
    mockColorHexService.getColorHex.and.returnValue(of('#FFFFFF'));

    mockDarkModeService = jasmine.createSpyObj('DarkModeService', [], { darkMode$: of(false) });
    mockCapcitorAppRefreshService = new MockCapcitorAppRefreshService();
    mockAlertComponent = jasmine.createSpyObj('BriAlertComponent', ['open', 'close']);

    await TestBed.configureTestingModule({
      imports: COMMON_TEST_IMPORTS,
      declarations: [NewMessageFooterComponent],
      providers: [
        ...COMMON_TEST_PROVIDERS,
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        {
          provide: DeviceStateService,
          useValue: mockDeviceStateService
        },
        {
          provide: SnackbarService,
          useValue: mockSnackbarService
        },
        {
          provide: ColorHexService,
          useValue: mockColorHexService
        },
        {
          provide: DarkModeService,
          useValue: mockDarkModeService
        },
        {
          provide: CapcitorAppRefreshService,
          useClass: MockCapcitorAppRefreshService
        },
        {
          provide: CameraService,
          useValue: new MockCameraService(),
        },
      ],
      schemas: COMMON_TEST_SCHEMAS
    }).compileComponents();
    fixture = TestBed.createComponent(NewMessageFooterComponent);
    component = fixture.componentInstance;

    // Set up test data
    component.formControl = new FormControl('Test message');

    // Set up the spy for the alert component
    component['component'] = mockAlertComponent;

    // We can't set the capacitorAppRefreshService directly on the component
    // because it's a private property with a different interface

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('contains input box for submitting messages', () => {
    const inputElement =
      fixture.debugElement.nativeElement.querySelector('#message-input');
    expect(inputElement).toBeTruthy();
  });

  it('contains send button', () => {
    const sendButton =
      fixture.debugElement.nativeElement.querySelector('#send-button');
    expect(sendButton).toBeTruthy();
  });

  it('contains image upload button', () => {
    const sendButton = fixture.debugElement.nativeElement.querySelector(
      '#image-upload-button',
    );
    expect(sendButton).toBeTruthy();
  });

  it('should emit sendButtonClick when onSendButtonClick is called with form value', () => {
    spyOn(component.sendButtonClick, 'emit');

    // Set the screenLocked property to true to trigger the unlock
    component['screenLocked'] = true;

    component.onSendButtonClick();
    expect(component.sendButtonClick.emit).toHaveBeenCalled();

    // Verify screenLocked was updated
    expect(component['screenLocked']).toBeFalse();
  });

  it('should emit sendButtonClick with images when onSendButtonClick is called with images', () => {
    spyOn(component.sendButtonClick, 'emit');
    const mockImage = { imageFile: new File([''], 'test.jpg', { type: 'image/jpeg' }), fileName: 'test.jpg' };
    component.userSubmittedImages = [mockImage];
    component.onSendButtonClick();
    expect(component.sendButtonClick.emit).toHaveBeenCalledWith([mockImage]);
    expect(component.userSubmittedImages.length).toBe(0);
  });

  it('should open error modal when onSendButtonClick is called with no message and no images', () => {
    component.formControl.setValue('');
    component.userSubmittedImages = [];
    spyOn(component, 'openErrorModal');
    component.onSendButtonClick();
    expect(component.openErrorModal).toHaveBeenCalled();
  });

  it('should open error modal with correct data', () => {
    // Create a mock BriAlertComponent
    const mockAlert = jasmine.createSpyObj('BriAlertComponent', ['open']);
    component['component'] = mockAlert;

    // Call the method
    component.openErrorModal();

    // Verify the modal was opened with the correct data
    expect(mockAlert.open).toHaveBeenCalledWith({
      header: 'We need more information.',
      description: 'It looks like you forgot to include a subject line or message. Please make sure to add both to send your message.',
      primaryButtonLabel: 'Okay',
      hideSecondaryButton: true,
    });
  });

  it('should close modal when closeModal is called', () => {
    // Create a mock BriAlertComponent
    const mockAlert = jasmine.createSpyObj('BriAlertComponent', ['close']);
    component['component'] = mockAlert;

    // Call the method
    component.closeModal();

    // Verify the modal was closed
    expect(mockAlert.close).toHaveBeenCalled();
  });

  it('should remove image at specified index', () => {
    const mockImage1 = { imageFile: new File([''], 'test1.jpg', { type: 'image/jpeg' }), fileName: 'test1.jpg' };
    const mockImage2 = { imageFile: new File([''], 'test2.jpg', { type: 'image/jpeg' }), fileName: 'test2.jpg' };
    component.userSubmittedImages = [mockImage1, mockImage2];
    component.removeImage(0);
    expect(component.userSubmittedImages.length).toBe(1);
    expect(component.userSubmittedImages[0]).toBe(mockImage2);
  });

  it('should update maxRow and deviceType on resize', () => {
    spyOn(component, 'calculateMaxRows').and.returnValue(12);
    mockDeviceStateService.determineDevice.and.returnValue('mobile');
    component.onResize();
    expect(component.maxRow).toBe(12);
    expect(component.deviceType).toBe('mobile');
    expect(component.calculateMaxRows).toHaveBeenCalled();
    expect(mockDeviceStateService.determineDevice).toHaveBeenCalled();
  });

  it('should calculate max rows based on screen height', () => {
    // Mock different screen heights and check the calculated rows
    const originalHeight = window.innerHeight;

    // Test different screen heights
    const testCases = [
      { height: 1100, expectedRows: 24 },
      { height: 1000, expectedRows: 20 },
      { height: 900, expectedRows: 16 },
      { height: 800, expectedRows: 12 },
      { height: 650, expectedRows: 8 },
      { height: 500, expectedRows: 4 }
    ];

    testCases.forEach(testCase => {
      // Mock the window.innerHeight
      Object.defineProperty(window, 'innerHeight', { value: testCase.height, configurable: true });

      // Calculate rows
      const rows = component.calculateMaxRows();

      // Verify the result
      expect(rows).toBe(testCase.expectedRows);
    });

    // Restore original height
    Object.defineProperty(window, 'innerHeight', { value: originalHeight, configurable: true });
  });

  it('should determine device type based on window width', () => {
    // Mock different screen widths and check the determined device type
    const originalWidth = window.innerWidth;

    // Test different screen widths
    const testCases = [
      { width: 500, expectedDevice: 'mobile' },
      { width: 700, expectedDevice: 'tablet' },
      { width: 1000, expectedDevice: 'desktop' }
    ];

    testCases.forEach(testCase => {
      // Mock the window.innerWidth
      Object.defineProperty(window, 'innerWidth', { value: testCase.width, configurable: true });

      // Determine device type
      const deviceType = component.determineDevice();

      // Verify the result
      expect(deviceType).toBe(testCase.expectedDevice);
    });

    // Restore original width
    Object.defineProperty(window, 'innerWidth', { value: originalWidth, configurable: true });
  });

  it('should lock app refresh', () => {
    // Make sure screenLocked is false to start
    component['screenLocked'] = false;

    // Call the method
    component.lockAppRefresh();

    // Verify screenLocked was updated
    expect(component['screenLocked']).toBeTrue();
  });

  describe('onFileSelected', () => {
    let mockFileInput: HTMLInputElement;
    let mockEvent: Event;

    beforeEach(() => {
      mockFileInput = document.createElement('input');
      mockFileInput.type = 'file';
      mockEvent = { target: mockFileInput } as unknown as Event;
    });

    it('should add valid image files to userSubmittedImages', () => {
      const file = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(mockFileInput, 'files', {
        value: [file],
        writable: false
      });

      component.onFileSelected(mockEvent);
      expect(component.userSubmittedImages.length).toBe(1);
      expect(component.userSubmittedImages[0].fileName).toBe('test.jpg');
    });

    it('should show error toast for invalid file types', () => {
      const file = new File([''], 'test.pdf', { type: 'application/pdf' });
      Object.defineProperty(mockFileInput, 'files', {
        value: [file],
        writable: false
      });

      component.onFileSelected(mockEvent);
      expect(mockSnackbarService.openErrorToast).toHaveBeenCalledWith('File of type application/pdf is not allowed.');
      expect(component.userSubmittedImages.length).toBe(0);
    });

    it('should show error toast when too many files are selected', () => {
      const files = [
        new File([''], 'test1.jpg', { type: 'image/jpeg' }),
        new File([''], 'test2.jpg', { type: 'image/jpeg' }),
        new File([''], 'test3.jpg', { type: 'image/jpeg' }),
        new File([''], 'test4.jpg', { type: 'image/jpeg' })
      ];
      Object.defineProperty(mockFileInput, 'files', {
        value: files,
        writable: false
      });

      component.onFileSelected(mockEvent);
      expect(mockSnackbarService.openErrorToast).toHaveBeenCalledWith('You can only submit up to 3 images per message');
    });

    it('should limit the number of images to 3', () => {
      // First add 2 images
      component.userSubmittedImages = [
        { imageFile: new File([''], 'existing1.jpg', { type: 'image/jpeg' }), fileName: 'existing1.jpg' },
        { imageFile: new File([''], 'existing2.jpg', { type: 'image/jpeg' }), fileName: 'existing2.jpg' }
      ];

      // Try to add 2 more (which would exceed the limit)
      const files = [
        new File([''], 'test1.jpg', { type: 'image/jpeg' }),
        new File([''], 'test2.jpg', { type: 'image/jpeg' })
      ];
      Object.defineProperty(mockFileInput, 'files', {
        value: files,
        writable: false
      });

      component.onFileSelected(mockEvent);
      expect(component.userSubmittedImages.length).toBe(3); // Only one more should be added
      expect(mockSnackbarService.openErrorToast).toHaveBeenCalledWith('You can only submit up to 3 images per message');
    });

    it('should reset the input value after processing', () => {
      const file = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(mockFileInput, 'files', {
        value: [file],
        writable: false
      });

      component.onFileSelected(mockEvent);
      expect(mockFileInput.value).toBe('');
    });
  });
});
