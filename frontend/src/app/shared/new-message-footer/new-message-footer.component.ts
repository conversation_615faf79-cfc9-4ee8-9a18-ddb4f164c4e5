import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  forwardRef,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { Bri<PERSON>lertComponent } from '@davita/bridge-library/alert';
import { ColorHexService } from '@davita/bridge-library/color-hex.service';
import { DarkModeService } from '@davita/bridge-library/dark-mode.service';
import { DialogData } from '@davita/bridge-library/shared';
import { CapcitorAppRefreshService } from '@davita/bridge-library/capacitor-app-refresh';
import { debounceTime, mergeMap } from 'rxjs';
import { ImageMessageData } from 'src/app/models';
import { DeviceStateService } from 'src/app/services/device-state.service';
import { SnackbarService } from 'src/app/services/snackbar.service';
import { CameraService } from 'src/app/services/camera.service';

const IMAGE_UPLOAD_LIMIT = 3;

@Component({
  selector: 'app-new-message-footer',
  templateUrl: './new-message-footer.component.html',
  styleUrls: ['./new-message-footer.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => NewMessageFooterComponent),
      multi: true,
    },
  ],
})
export class NewMessageFooterComponent
  implements ControlValueAccessor, OnInit, OnDestroy {
  @ViewChild(BriAlertComponent, { static: false })
  private component!: BriAlertComponent;
  @Input() formControl: FormControl = new FormControl('');
  @Input() isReadOnly: boolean = false;
  @Output() sendButtonClick = new EventEmitter<ImageMessageData[] | void>();
  fileImageColor = '';
  userSubmittedImages: ImageMessageData[] = [] as ImageMessageData[]; // stores images uploaded by user
  maxRow: number = 24;
  deviceType: string = this.deviceState.determineDevice();
  allowedFormats: string[] = [
    'image/gif',
    'image/png',
    'image/eps',
    'image/tiff',
    'image/bmp',
    'image/jpeg',
    'image/jpg',
  ];
  maxLengthValidationError: string =
    'Message too long, please limit to 2054 characters.';

  public screenLocked = false;

  constructor(
    private deviceState: DeviceStateService,
    private snackbar: SnackbarService,
    private colorHexService: ColorHexService,
    private darkModeService: DarkModeService,
    private capacitorAppRefreshService: CapcitorAppRefreshService,
    private cameraService: CameraService,
  ) { }

  ngOnDestroy(): void {
    this.capacitorAppRefreshService.setlockCapacitorAppRefresh(false);
  }

  lockAppRefresh = () => {
    if (!this.screenLocked) {
      this.screenLocked = true;
      this.capacitorAppRefreshService.setlockCapacitorAppRefresh(true);
    }
  };

  /**
   * Handle photo selection - use Capacitor Camera on mobile, fallback to file input on web
   */
  async onPhotoButtonClick(): Promise<void> {
    if (this.cameraService.isNativePlatform()) {
      await this.handleNativePhotoSelection();
    } else {
      // Fallback to file input for web browsers
      const fileInput = document.querySelector('#file-input') as HTMLInputElement;
      if (fileInput) {
        fileInput.click();
      }
    }
  }

  /**
   * Handle native photo selection using Capacitor Camera
   */
  private async handleNativePhotoSelection(): Promise<void> {
    try {
      this.lockAppRefresh();

      // Check permissions first
      const hasPermissions = await this.cameraService.checkPermissions();
      if (!hasPermissions) {
        const granted = await this.cameraService.requestPermissions();
        if (!granted) {
          this.snackbar.openErrorToast('Camera permissions are required to take photos');
          return;
        }
      }

      // Use Capacitor Camera to select photo (shows action sheet with camera/gallery options)
      const photo = await this.cameraService.selectPhoto();

      if (photo && photo.file) {
        await this.addImageToList(photo.file);
        // Optional: Show success message
        // this.snackbar.openSuccessToast('Photo added successfully');
      } else if (photo === null) {
        // User cancelled photo selection - this is normal, no error message needed
        console.log('User cancelled photo selection');
      }
    } catch (error) {
      console.error('Error handling native photo selection:', error);
      this.snackbar.openErrorToast('Failed to select photo. Please try again.');
    }
  }

  /**
   * Add image file to the list with validation
   */
  private async addImageToList(file: File): Promise<void> {
    // Validate file type
    if (!this.allowedFormats.includes(file.type.toLowerCase())) {
      this.snackbar.openErrorToast(`File of type ${file.type} is not allowed.`);
      return;
    }

    // Check image limit
    if (this.userSubmittedImages.length >= IMAGE_UPLOAD_LIMIT) {
      this.snackbar.openErrorToast(
        `You can only submit up to ${IMAGE_UPLOAD_LIMIT} images per message`
      );
      return;
    }

    // Add to images list
    this.userSubmittedImages.push({
      imageFile: file,
      fileName: file.name,
    });
  }

  async onFileSelected(event: Event): Promise<void> {
    const input = event.target as HTMLInputElement;

    try {
      this.lockAppRefresh();

      // Check if the input has files
      if (input.files && input.files.length > 0) {
        // Check if too many files selected
        if (input.files.length > IMAGE_UPLOAD_LIMIT) {
          this.snackbar.openErrorToast(
            `You can only submit up to ${IMAGE_UPLOAD_LIMIT} images per message`,
          );
          return;
        }

        // Process each file
        for (let i = 0; i < input.files.length; i++) {
          const file = input.files[i];
          await this.addImageToList(file);
        }
      }
    } catch (error) {
      console.error('Error processing selected files:', error);
      this.snackbar.openErrorToast('Failed to process selected files. Please try again.');
    } finally {
      // Reset input so user can re-select the same file if needed
      input.value = '';
    }
  }

  removeImage(index: number): void {
    if (index < this.userSubmittedImages.length)
      this.userSubmittedImages.splice(index, 1);
    if (
      this.formControl.value.length === 0 &&
      this.userSubmittedImages.length === 0 &&
      this.screenLocked
    ) {
      this.screenLocked = false;
      this.capacitorAppRefreshService.setlockCapacitorAppRefresh(false);
    }
  }

  onSendButtonClick(): void {
    if (this.screenLocked) {
      this.capacitorAppRefreshService.setlockCapacitorAppRefresh(false);
      this.screenLocked = false;
    }
    if (this.userSubmittedImages.length > 0) {
      this.sendButtonClick.emit(this.userSubmittedImages);
      this.userSubmittedImages = [];
    } else if (this.formControl.value) this.sendButtonClick.emit();
    else {
      this.openErrorModal();
    }
  }

  openErrorModal() {
    const data: DialogData = {
      header: 'We need more information.',
      description:
        'It looks like you forgot to include a subject line or message. Please make sure to add both to send your message.',
      primaryButtonLabel: 'Okay',
      hideSecondaryButton: true,
    };
    this.component.open(data);
  }

  closeModal() {
    this.component.close();
  }

  // THIS IS BOILERPLATE CODE REQUIRED TO AVOID NG01203 ERROR
  // https://angular.io/errors/NG01203
  // TODO - we may be able to fix this in the bridge library
  // eslint-disable-next-line
  writeValue(_obj: any): void {
    // Write value from the model to the view
  }

  // eslint-disable-next-line
  registerOnChange(_fn: any): void {
    // Register a handler that should be called when something in the view changes
  }

  // eslint-disable-next-line
  registerOnTouched(_fn: any): void {
    // Register a handler specifically for when the component is touched
  }

  // eslint-disable-next-line
  setDisabledState?(_isDisabled: boolean): void {
    // Handle the disabled state
  }

  ngOnInit() {
    this.formControl.valueChanges.pipe(debounceTime(100)).subscribe((value) => {
      if (!this.screenLocked && value.length > 0) {
        this.screenLocked = true;
        this.capacitorAppRefreshService.setlockCapacitorAppRefresh(true);
      }
      if (
        value.length === 0 &&
        this.userSubmittedImages.length === 0 &&
        this.screenLocked
      ) {
        this.screenLocked = false;
        this.capacitorAppRefreshService.setlockCapacitorAppRefresh(false);
      }
    });
    this.maxRow = this.calculateMaxRows();

    this.darkModeService.darkMode$
      .pipe(
        mergeMap((darkMode) => {
          const mode = darkMode ? 'dark' : 'light';
          return this.colorHexService.getColorHex(`${mode}-color-text-50`);
        }),
      )
      .subscribe((hexColor) => {
        this.fileImageColor = hexColor;
      });
  }

  @HostListener('window:resize')
  onResize(): void {
    this.maxRow = this.calculateMaxRows();
    this.deviceType = this.deviceState.determineDevice();
  }

  calculateMaxRows(): number {
    const screenHeight = window.innerHeight;
    if (screenHeight >= 1050) {
      return 24;
    } else if (screenHeight >= 950) {
      return 20;
    } else if (screenHeight >= 850) {
      return 16;
    } else if (screenHeight >= 700) {
      return 12;
    } else if (screenHeight >= 600) {
      return 8;
    } else {
      return 4;
    }
  }

  determineDevice(): string {
    if (window.innerWidth < 600) {
      return 'mobile';
    } else if (600 <= window.innerWidth && window.innerWidth < 960) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }
}
