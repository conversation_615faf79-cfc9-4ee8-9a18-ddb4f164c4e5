import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from 'src/app/shared/mocks/services';
import { ImageComponent } from './image.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ImageStateService } from 'src/app/services/image-state.service';
import { of } from 'rxjs';
import { COMMON_TEST_IMPORTS, COMMON_TEST_PROVIDERS, COMMON_TEST_SCHEMAS } from 'src/app/testing/test-helpers';
import { MessagingProxyService } from 'src/app/services/messaging-proxy.service';
import { Status } from 'src/app/models';

describe('ImageComponent', () => {
  let component: ImageComponent;
  let fixture: ComponentFixture<ImageComponent>;

  let mockImageStateService: jasmine.SpyObj<ImageStateService>;
  let mockHttpMessagingService: jasmine.SpyObj<HttpMessagingService>;
  let mockMatDialog: jasmine.SpyObj<MatDialog>;
  let mockMessagingProxyService: jasmine.SpyObj<MessagingProxyService>;

  beforeEach(async () => {
    mockImageStateService = jasmine.createSpyObj('ImageStateService', ['setImage', 'images$']);
    mockImageStateService.images$ = of({ 'test-blob-id': 'MOCK-IMAGE' });

    mockHttpMessagingService = jasmine.createSpyObj('HttpMessagingService', ['getImage']);
    mockHttpMessagingService.getImage.and.returnValue(of('MOCK-IMAGE'));

    mockMatDialog = jasmine.createSpyObj('MatDialog', ['open']);

    mockMessagingProxyService = jasmine.createSpyObj('MessagingProxyService', ['getHttpService']);
    mockMessagingProxyService.getHttpService.and.returnValue(mockHttpMessagingService);

    await TestBed.configureTestingModule({
      imports: [
        ...COMMON_TEST_IMPORTS,
        MatDialogModule
      ],
      declarations: [ImageComponent],
      providers: [
        ...COMMON_TEST_PROVIDERS,
        {
          provide: HttpMessagingService,
          useValue: mockHttpMessagingService,
        },
        {
          provide: ImageStateService,
          useValue: mockImageStateService,
        },
        {
          provide: MatDialog,
          useValue: mockMatDialog,
        },
        {
          provide: MessagingProxyService,
          useValue: mockMessagingProxyService
        }
      ],
      schemas: COMMON_TEST_SCHEMAS
    }).compileComponents();

    fixture = TestBed.createComponent(ImageComponent);
    component = fixture.componentInstance;

    // Set the proxyService directly on the component
    component['proxyService'] = mockMessagingProxyService;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain image when imageStatus is success', () => {
    // Set up the component with a mock image
    component.image = 'MOCK-IMAGE';
    component.imageStatus = Status.success;

    // Trigger change detection
    fixture.detectChanges();

    // Check if the image element is present and has the correct src
    const imgElement = fixture.nativeElement.querySelector('img#image');
    expect(imgElement).toBeTruthy();
    expect(imgElement.src).toContain('MOCK-IMAGE');
  });

  it('should show loading skeleton when imageStatus is loading', () => {
    // Set up the component with loading status
    component.imageStatus = Status.loading;
    component.assetsUrl = '/assets'; // Mock the assets URL

    // Trigger change detection
    fixture.detectChanges();

    // Check if the skeleton loader is present
    const skeletonElement = fixture.nativeElement.querySelector('img.skeleton-loader');
    expect(skeletonElement).toBeTruthy();
    expect(skeletonElement.src).toContain('/images/loading-skeleton.gif');
  });

  it('should show error state when imageStatus is error', () => {
    // Set up the component with error status
    component.imageStatus = Status.error;

    // Trigger change detection
    fixture.detectChanges();

    // Check if the error element is present
    const errorElement = fixture.nativeElement.querySelector('div.error-image');
    expect(errorElement).toBeTruthy();

    // Check if the error element contains the expected text
    expect(errorElement.textContent).toContain('Tap to Load');
  });

  it('should load image directly when imageObjectUrl is provided', () => {
    // Set up the component with an imageObjectUrl
    component.imageObjectUrl = 'DIRECT-IMAGE-URL';

    // Call loadImage method
    component.loadImage();

    // Check that the image was set correctly
    expect(component.image).toBe('DIRECT-IMAGE-URL');
    expect(component.imageStatus).toBe(Status.success);
  });

  it('should fetch image from state service when available', () => {
    // Set up the component with blobId
    component.blobId = 'test-blob-id';
    component.messageId = 'test-message-id';

    // Call loadImage method
    component.loadImage();

    // Check that the image was set correctly from the state service
    expect(component.image).toBe('MOCK-IMAGE');
    expect(component.imageStatus).toBe(Status.success);
  });

  it('should open image modal when toggleImageModal is called with valid image', () => {
    // Call toggleImageModal with a valid image source
    component.toggleImageModal('MOCK-IMAGE');

    // Check that the dialog.open method was called with the correct parameters
    expect(mockMatDialog.open).toHaveBeenCalledWith(
      jasmine.any(Function), // ImageModalComponent constructor
      {
        width: '100%',
        height: '100%',
        data: { imgSrc: 'MOCK-IMAGE' }
      }
    );
  });

  it('should not open image modal when toggleImageModal is called with undefined', () => {
    // Call toggleImageModal with undefined
    component.toggleImageModal(undefined);

    // Check that the dialog.open method was not called
    expect(mockMatDialog.open).not.toHaveBeenCalled();
  });
});
