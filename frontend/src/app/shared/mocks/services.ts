import { BehaviorSubject, Observable, Subject, of } from 'rxjs';
import { mockThread, mockUsers, mockThreads, mockMessages } from './data';
import { DeliveryStatus, ImageByBlobId, Message, MessagingUser, PaginatedMessagesByThread, Thread } from 'src/app/models';
import { environment } from '../../../environments/environment';
import { Device } from 'src/app/services/device-state.service';

// Mock for BriPatientAuthorizationService
export class MockBriPatientAuthorizationService {
  user$ = of({ cerner_patient_reference: '121234342', externalId: '13234872', experience: { value: 'test' } });
}

const paramsSubject = new BehaviorSubject({ id: mockThread.id });
const dataSubject = new BehaviorSubject({
  thread: mockThread,
});

export const activatedRouteMock = {
  params: paramsSubject.asObservable(),
  data: dataSubject.asObservable(),
  snapshot: {
    data: {
      thread: mockThread,
    },
  },
};

export class MockCareTeamService {
  getUserCareTeam(): Observable<MessagingUser[]> {
    return of(mockUsers);
  }
}

export class MockHttpMessagingService {
  rootUrl = '';
  messagesPerPage = environment.messaging.messages_per_api_request;
  private isThreadsEmptySubject = new BehaviorSubject<boolean>(false);
  http: any = { get: () => of({}), post: () => of({}) };

  get isThreadEmpty() {
    return this.isThreadsEmptySubject.value;
  }

  set isThreadsEmpty(threadStatus: boolean) {
    this.isThreadsEmptySubject.next(threadStatus);
  }

  getUserThreads(): Observable<Thread[]> {
    return of(mockThreads);
  }

  getPaginatedMessagesByThreadId(
    threadId: string,
    page = 0,
    messagePerPage = this.messagesPerPage,
  ): Observable<Message[]> {
    return of(mockMessages);
  }

  updateThreadUnreadMessages(
    threadId: string,
    unreadMessages: number = 0,
  ): Observable<number> {
    return of(0);
  }

  getImage(blobId: string, messageId: string): Observable<string> {
    return of('MOCK-IMAGE');
  }

  postNewThreadPlusFirstMessage(
    threadSubject: string,
    firstMessage: string,
    files: File[] = [],
  ): Observable<Message> {
    const formData = new FormData();

    formData.append('message', firstMessage);
    formData.append('subject', threadSubject);
    files.forEach((file) => {
      formData.append('files', file);
    });

    return of(mockMessages[0]);
  }

  postMessagePlusFileByThreadId(
    newMessage: string,
    threadId: string,
    files: File[] = [],
  ): Observable<Message> {
    const formData = new FormData();

    formData.append('message', newMessage);
    files.forEach((file) => {
      formData.append('files', file);
    });

    return of(mockMessages[0]);
  }
}

export class MockMessagingStateService {
  private threadsSubject = new BehaviorSubject<Thread[]>(mockThreads);
  private messagesSubject = new BehaviorSubject<PaginatedMessagesByThread>({
    [mockThread.id]: { 0: mockMessages }
  });

  // Publicly exposed observables for the components to subscribe to
  threads$ = this.threadsSubject.asObservable();
  messages$ = this.messagesSubject.asObservable();

  // id of logged in user
  private userId = '13234872';

  updateThreads(threads: Thread[]): void {
    this.threadsSubject.next(threads);
  }

  addNewThread(newThread: Thread): void {
    const newThreads = [newThread, ...this.threadsSubject.value];
    this.updateThreads(newThreads as Thread[]);
  }

  updateThreadUnreadMessageCount(threadId: string, unreadMessages: number) {
    const threads = [...this.threadsSubject.value];
    const index = threads.findIndex((_thread) => _thread.id == threadId);
    threads[index].unreadMessages = unreadMessages;
    this.updateThreads(threads);
  }

  updateMessages(
    messages: Message[],
    threadId: string,
    page: number,
  ): Observable<PaginatedMessagesByThread> {
    const currentMessages = { ...this.messagesSubject.value };
    const updatedMessages = {
      ...this.messagesSubject.value,
      [threadId]: {
        ...currentMessages[threadId],
        [page]: messages,
      },
    };

    this.messagesSubject.next(updatedMessages);
    return this.messages$;
  }

  addNewMessage(newMessage: Message, threadId: string) {
    const currentMessages = { ...this.messagesSubject.value };
    if (!currentMessages[threadId]) {
      currentMessages[threadId] = { 0: [] };
    }
    currentMessages[threadId][0].push(newMessage);
    this.messagesSubject.next(currentMessages);
    this.updateThreadAfterAddingNewMessage(newMessage, threadId);
  }

  addNewMessageOptimisticUpdate(
    messageText: string,
    threadId: string,
    files: File[] = [],
  ): string {
    const id = 'mock-message-id';
    const currentMessages = { ...this.messagesSubject.value };
    if (!currentMessages[threadId]) {
      currentMessages[threadId] = { 0: [] };
    }

    if (messageText) {
      const optimisticMessage = {
        id,
        threadId,
        sender: { externalId: this.userId } as MessagingUser,
        cernerReferenceId: id,
        messageText,
        deliveryStatus: DeliveryStatus.pending,
        sentAt: new Date().toString(),
      };
      currentMessages[threadId][0].push(optimisticMessage);
    }

    for (const file of files) {
      const optimisticImageMessage = {
        id,
        threadId,
        sender: { externalId: this.userId } as MessagingUser,
        cernerReferenceId: id,
        messageText: '',
        deliveryStatus: DeliveryStatus.pending,
        undeliveredImage: {
          file,
          imageObjectUrl: 'mock-image-url',
        },
        sentAt: new Date().toString(),
      };
      currentMessages[threadId][0].push(optimisticImageMessage);
    }

    this.messagesSubject.next(currentMessages);
    return id;
  }

  updateOptimisticMessageState(
    messageId: string,
    threadId: string,
    newStatus: DeliveryStatus,
  ) {
    const currentMessages = { ...this.messagesSubject.value };
    if (!currentMessages[threadId] || !currentMessages[threadId][0]) return;

    const updatedMessages = currentMessages[threadId][0].map((message) => {
      if (message.id === messageId) {
        return {
          ...message,
          deliveryStatus: newStatus,
        };
      }
      return message;
    });

    currentMessages[threadId][0] = updatedMessages;
    this.messagesSubject.next(currentMessages);
  }

  getUndeliveredMessages(threadId: string): Message[] {
    if (!this.messagesSubject.value[threadId] || !this.messagesSubject.value[threadId][0]) {
      return [];
    }
    return this.messagesSubject.value[threadId][0].filter(
      (message) => message.deliveryStatus === DeliveryStatus.undelivered,
    );
  }

  updateThreadAfterAddingNewMessage(message: Message, id: string): void {
    const currentThreads = [...this.threadsSubject.getValue()];
    const threadIndex = currentThreads.findIndex((thread) => thread.id === id);
    if (threadIndex !== -1) {
      if (message.messageText) {
        currentThreads[threadIndex].lastMessage = message.messageText;
      } else {
        currentThreads[threadIndex].lastMessage = '';
      }
      currentThreads[threadIndex].lastMessageSentAt = message.sentAt;
      currentThreads[threadIndex].unreadMessages += 1;
      this.updateThreads(currentThreads);
    }
  }
}

export class MockDeviceStateService {
  private threadListVisibleSubject = new BehaviorSubject<boolean>(true);

  get threadListVisible() {
    return this.threadListVisibleSubject.value;
  }

  set threadListVisible(value: boolean) {
    this.threadListVisibleSubject.next(value);
  }

  determineDevice(): Device {
    return 'desktop';
  }
}

export class MockSnackbarService {
  openErrorToast(message: string): void { }
  openSuccessToast(message: string): void { }
}

export class MockImageStateService {
  private imageSubject = new BehaviorSubject<ImageByBlobId>({});
  images$ = this.imageSubject.asObservable();

  setImage(blobId: string, objectUrl: string): void {
    const updatedImages = { ...this.imageSubject.value, [blobId]: objectUrl };
    this.imageSubject.next(updatedImages);
  }
}

export class MockWebSocketService {
  private stompClient: any = null;
  private rootUrl = '';
  private userId = '';

  constructor() { }

  initWebSocket(): void { }
  initializeWebSocketConnection(): void { }
  disconnect(): void { }
}

export class MockBriPlatformService {
  platform = 'desktop';
}

export class MockCapcitorAppRefreshService {
  setlockCapacitorAppRefresh(value: boolean): void { }
}
