import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { MockHttpMessagingService } from 'src/app/shared/mocks/services';
import { ImageModalComponent } from './image-modal.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { mockDialogData } from 'src/app/shared/mocks/data';
import { By } from '@angular/platform-browser';
import { MatIconModule } from '@angular/material/icon';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('ImageComponent', () => {
  let component: ImageModalComponent;
  let fixture: ComponentFixture<ImageModalComponent>;
  // Create a mock for MatDialogRef
  const mockMatDialogRef = {
    close: jasmine.createSpy('close'),
  };
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MatDialogModule, MatIconModule],
      declarations: [ImageModalComponent],
      providers: [
        {
          provide: HttpMessagingService,
          useValue: new MockHttpMessagingService(),
        },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        { provide: MatDialogRef, useValue: mockMatDialogRef },
        HttpClient,
        HttpHandler,
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    fixture = TestBed.createComponent(ImageModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain image', () => {
    // Check if the image element is present
    const imgElement = fixture.nativeElement.querySelector('img.centered-image');
    expect(imgElement).toBeTruthy();
  });

  it('should contain correct image tag with the right source', () => {
    // Check if the image element has the correct src attribute
    const imgElement = fixture.nativeElement.querySelector('img.centered-image');
    expect(imgElement).toBeTruthy();
    expect(imgElement.src).toContain('MOCK-IMAGE-DATA');
    expect(imgElement.alt).toBe('user submitted image');
  });

  it('should contain close button', () => {
    // Check if the close button is present
    const closeButton = fixture.nativeElement.querySelector('bri-buttons.close-button');
    expect(closeButton).toBeTruthy();
  });

  it('should close dialog when close button is clicked', () => {
    // Trigger the buttonClicked event on the close button
    const closeButton = fixture.debugElement.query(By.css('bri-buttons.close-button'));
    closeButton.triggerEventHandler('buttonClicked', null);

    // Check that the dialog was closed
    expect(mockMatDialogRef.close).toHaveBeenCalled();
  });
});
