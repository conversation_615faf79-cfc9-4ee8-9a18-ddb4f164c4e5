import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { ThreadMessagesComponent } from './thread-messages.component';
import { ActivatedRoute, Router } from '@angular/router';
import {
  MockBriPatientAuthorizationService,
  activatedRouteMock,
} from 'src/app/messaging/mocks/services';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { BriPatientAuthorizationService } from '@davita/bridge-utility-library/authorization';
import { BriSnackBarModule } from '@davita/bridge-library/snack-bar';
import { BriAlertModule, BriAlertComponent } from '@davita/bridge-library/alert';
import { MatDividerModule } from '@angular/material/divider';
import { BriButtonsModule } from '@davita/bridge-library/buttons';
import { BriProgressBarModule } from '@davita/bridge-library/progress-bar';
import { BriTextareaFieldsModule } from '@davita/bridge-library/fields/textarea';
import {
  BrowserAnimationsModule,
  NoopAnimationsModule,
} from '@angular/platform-browser/animations';
import { BriSubHeaderModule } from '@davita/bridge-library/sub-header';
import { MessageDatePipe } from '../message-date.pipe';
import { CommonModule, DatePipe } from '@angular/common';
import { BriChatMessageModule } from '@davita/bridge-library/chat-message';
import { mockMessages, mockThread } from 'src/app/messaging/mocks/data';
import { FormControl } from '@angular/forms';
import { DeviceStateService } from 'src/app/services/device-state.service';
import { SnackbarService } from 'src/app/services/snackbar.service';
import { MessagingStateService } from 'src/app/services/messaging-state.service';
import { of, throwError } from 'rxjs';
import { By } from '@angular/platform-browser';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';

describe('ThreadMessagesComponent', () => {
  let component: ThreadMessagesComponent;
  let fixture: ComponentFixture<ThreadMessagesComponent>;

  let mockRouter: jasmine.SpyObj<Router>;
  let mockHttpMessagingService: jasmine.SpyObj<HttpMessagingService>;
  let mockSnackbarService: jasmine.SpyObj<SnackbarService>;
  let mockDeviceStateService: jasmine.SpyObj<DeviceStateService>;
  let mockMessagingStateService: jasmine.SpyObj<MessagingStateService>;
  let mockAlertComponent: jasmine.SpyObj<BriAlertComponent>;

  beforeEach(async () => {
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockHttpMessagingService = jasmine.createSpyObj('HttpMessagingService', [
      'postMessagePlusFileByThreadId',
      'getUserThreads'
    ]);
    // Create a mock message that matches the Message interface
    const mockMessage = {
      id: 'mock-id',
      cernerReferenceId: 'mock-ref',
      threadId: mockThread.id,
      sender: mockMessages[0].sender,
      messageText: 'Test message',
      sentAt: new Date().toISOString()
    };
    mockHttpMessagingService.postMessagePlusFileByThreadId.and.returnValue(of(mockMessage));
    mockHttpMessagingService.getUserThreads.and.returnValue(of([mockThread]));

    mockSnackbarService = jasmine.createSpyObj('SnackbarService', ['openSuccessToast', 'openErrorToast']);
    mockDeviceStateService = jasmine.createSpyObj('DeviceStateService', ['determineDevice']);
    mockDeviceStateService.determineDevice.and.returnValue('desktop');
    mockDeviceStateService.threadListVisible = false;

    mockMessagingStateService = jasmine.createSpyObj('MessagingStateService', [
      'addNewMessageOptimisticUpdate',
      'updateOptimisticMessageState',
      'getUndeliveredMessages',
      'updateThreads'
    ]);
    mockMessagingStateService.addNewMessageOptimisticUpdate.and.returnValue('mock-message-id');
    mockMessagingStateService.getUndeliveredMessages.and.returnValue([]);

    mockAlertComponent = jasmine.createSpyObj('BriAlertComponent', ['open', 'close']);

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        BriSnackBarModule,
        BriAlertModule,
        MatDividerModule,
        BriButtonsModule,
        BriProgressBarModule,
        BriTextareaFieldsModule,
        BrowserAnimationsModule,
        NoopAnimationsModule,
        BriSubHeaderModule,
        BriChatMessageModule,
      ],
      declarations: [
        ThreadMessagesComponent,
        MessageDatePipe,
      ],
      providers: [
        DatePipe,
        {
          provide: BriPatientAuthorizationService,
          useClass: MockBriPatientAuthorizationService,
        },
        { provide: ActivatedRoute, useValue: activatedRouteMock },
        { provide: Router, useValue: mockRouter },
        { provide: HttpMessagingService, useValue: mockHttpMessagingService },
        { provide: SnackbarService, useValue: mockSnackbarService },
        { provide: DeviceStateService, useValue: mockDeviceStateService },
        { provide: MessagingStateService, useValue: mockMessagingStateService },
        HttpClient,
        HttpHandler,
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
    }).compileComponents();
    fixture = TestBed.createComponent(ThreadMessagesComponent);
    component = fixture.componentInstance;

    // Set up test data
    component.newMessageFormControl = new FormControl('Test message');
    component.thread = mockThread;
    component.id = mockThread.id;

    // Set up the spy for the alert component
    component['component'] = mockAlertComponent;

    // Create a mock element ref for scrollContainer
    component.scrollContainerRef = {
      nativeElement: {
        scrollHeight: 1000,
        scrollTop: 0,
        addEventListener: jasmine.createSpy('addEventListener')
      }
    } as any;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should contain thread title at top', () => {
    // Set the thread subject
    component.thread = { ...mockThread, subject: 'Test Thread Subject' };
    fixture.detectChanges();

    // Find the header component
    const headerElement = fixture.debugElement.query(By.css('app-message-header'));

    // Check if the header component exists and has the thread property set
    expect(headerElement).toBeTruthy();
    expect(headerElement.componentInstance.thread).toEqual(component.thread);
  });



  it('should close modal when closeModal is called', () => {
    // Create a spy for the BriAlertComponent
    const alertComponentSpy = jasmine.createSpyObj('BriAlertComponent', ['close']);

    // Set the component property
    component['component'] = alertComponentSpy;

    // Call the closeModal method
    component.closeModal();

    // Check that the alert component's close method was called
    expect(alertComponentSpy.close).toHaveBeenCalled();
  });

  it('should navigate back to messaging when closeMessage is called', () => {
    // Call the closeMessage method
    component.closeMessage();

    // Check that the router was called with the correct navigation path
    expect(mockRouter.navigate).toHaveBeenCalledWith(
      jasmine.arrayContaining(['../../landing']),
      jasmine.objectContaining({
        skipLocationChange: true
      })
    );

    // Check that threadListVisible was set to true
    expect(mockDeviceStateService.threadListVisible).toBe(true);
  });

  it('should update deviceType on resize', () => {
    // Set up the spy on determineDevice
    mockDeviceStateService.determineDevice.and.returnValue('mobile');

    // Trigger the window resize event
    window.dispatchEvent(new Event('resize'));

    // Check that the deviceType was updated
    expect(mockDeviceStateService.determineDevice).toHaveBeenCalled();
    expect(component.deviceType).toBe('mobile');
  });

  it('should scroll to bottom', () => {
    // Set up the scrollContainer
    const mockScrollContainer = {
      scrollHeight: 1000,
      scrollTop: 0
    };
    component.scrollContainerRef = { nativeElement: mockScrollContainer } as any;

    // Call the scrollToBottom method
    component.scrollToBottom();

    // Check that the scrollTop was set to the scrollHeight
    expect(mockScrollContainer.scrollTop).toBe(mockScrollContainer.scrollHeight);
  });
});
