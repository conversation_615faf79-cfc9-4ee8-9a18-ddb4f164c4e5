import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { NewMessageComponent } from './new-message.component';
import { HttpClient } from '@angular/common/http';
import { HttpMessagingService } from 'src/app/services/http-messaging.service';
import { BriAlertComponent } from '@davita/bridge-library/alert';
import { SnackbarService } from 'src/app/services/snackbar.service';
import { of } from 'rxjs';
import { COMMON_TEST_IMPORTS, COMMON_TEST_PROVIDERS, COMMON_TEST_SCHEMAS } from 'src/app/testing/test-helpers';
import { MessagingProxyService } from 'src/app/services/messaging-proxy.service';
import { UserTypeService } from 'src/app/services/user-type.service';

describe('NewMessageComponent', () => {
  let component: NewMessageComponent;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockHttpMessagingService: jasmine.SpyObj<HttpMessagingService>;
  let mockSnackbarService: jasmine.SpyObj<SnackbarService>;
  let mockAlertComponent: jasmine.SpyObj<BriAlertComponent>;

  let fixture: ComponentFixture<NewMessageComponent>;
  let mockMessagingProxyService: jasmine.SpyObj<MessagingProxyService>;
  let mockUserTypeService: jasmine.SpyObj<UserTypeService>;
  let mockActivatedRoute: jasmine.SpyObj<ActivatedRoute>;

  beforeEach(() => {
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockHttpMessagingService = jasmine.createSpyObj('HttpMessagingService', [
      'postNewThreadPlusFirstMessage'
    ]);
    mockSnackbarService = jasmine.createSpyObj('SnackbarService', ['openSuccessToast', 'openErrorToast']);
    mockAlertComponent = jasmine.createSpyObj('BriAlertComponent', ['open', 'close']);
    mockMessagingProxyService = jasmine.createSpyObj('MessagingProxyService', ['getHttpService']);
    mockUserTypeService = jasmine.createSpyObj('UserTypeService', [], { userType$: of('PATIENT') });
    mockActivatedRoute = jasmine.createSpyObj('ActivatedRoute', [], { snapshot: { params: { id: '123' } } });

    // Set up the mock proxy service to return the mock HTTP service
    mockMessagingProxyService.getHttpService.and.returnValue(mockHttpMessagingService);

    TestBed.configureTestingModule({
      imports: COMMON_TEST_IMPORTS,
      declarations: [NewMessageComponent],
      providers: [
        ...COMMON_TEST_PROVIDERS,
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: MessagingProxyService, useValue: mockMessagingProxyService },
        { provide: SnackbarService, useValue: mockSnackbarService },
        { provide: UserTypeService, useValue: mockUserTypeService },
        { provide: HttpClient, useValue: {} }
      ],
      schemas: COMMON_TEST_SCHEMAS
    }).compileComponents();

    fixture = TestBed.createComponent(NewMessageComponent);
    component = fixture.componentInstance;
    component['component'] = mockAlertComponent;
    component['messageSentComponent'] = mockAlertComponent;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form controls', () => {
    expect(component.subjectFormControl).toBeDefined();
    expect(component.newMessageFormControl).toBeDefined();
  });

  it('should show error modal when trying to send without subject', () => {
    // Set message but no subject
    component.newMessageFormControl.setValue('Test message');
    component.subjectFormControl.setValue('');

    // Spy on the MissingFieldErrorModal method
    spyOn(component, 'MissingFieldErrorModal');

    // Try to create a new thread
    component.createNewThreadWithMessage();

    // Verify the error modal method was called
    expect(component.MissingFieldErrorModal).toHaveBeenCalled();
  });

  it('should show error modal when subject is too long', () => {
    // Set message and a subject that's too long (over 140 characters)
    component.newMessageFormControl.setValue('Test message');
    component.subjectFormControl.setValue('A'.repeat(141));

    // Spy on the SubjectTooLongErrorModal method
    spyOn(component, 'SubjectTooLongErrorModal');

    // Try to create a new thread
    component.createNewThreadWithMessage();

    // Verify the error modal method was called
    expect(component.SubjectTooLongErrorModal).toHaveBeenCalled();
  });

  it('should navigate to thread page after successful message send', () => {
    // Set valid message and subject
    component.newMessageFormControl.setValue('Test message');
    component.subjectFormControl.setValue('Test subject');

    // Mock the HTTP service to return a successful response
    mockHttpMessagingService.postNewThreadPlusFirstMessage.and.returnValue(of({
      threadId: 'test-thread-id'
    } as any));

    // Try to create a new thread
    component.createNewThreadWithMessage();

    // Verify the HTTP service was called
    expect(mockHttpMessagingService.postNewThreadPlusFirstMessage).toHaveBeenCalledWith(
      'Test subject',
      'Test message',
      [],
      undefined
    );

    // Verify the success toast was shown
    expect(mockSnackbarService.openSuccessToast).toHaveBeenCalledWith('Message Sent');

    // Verify navigation to the thread page
    expect(mockRouter.navigate).toHaveBeenCalledWith(
      ['../thread/test-thread-id'],
      { skipLocationChange: true, relativeTo: mockActivatedRoute }
    );
  });

  it('should open missing field error modal with correct data', () => {
    // Create a new spy for the component
    const alertSpy = jasmine.createSpyObj('BriAlertComponent', ['open', 'close']);
    component['component'] = alertSpy;

    // Call the method to open the error modal
    component.MissingFieldErrorModal();

    // Verify the modal was opened with the correct data
    expect(alertSpy.open).toHaveBeenCalledWith({
      header: 'We need more information.',
      description: 'It looks like you forgot to include a subject line or message. Please make sure to add both to send your message.',
      primaryButtonLabel: 'Okay',
      hideSecondaryButton: true,
    });
  });

  it('should close modal when closeModal is called', () => {
    // Create a new spy for the component
    const alertSpy = jasmine.createSpyObj('BriAlertComponent', ['open', 'close']);
    component['component'] = alertSpy;

    // Call the method to close the modal
    component.closeModal();

    // Verify the modal was closed
    expect(alertSpy.close).toHaveBeenCalled();
  });
});
