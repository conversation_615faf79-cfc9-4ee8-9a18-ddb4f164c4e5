import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { DatePipe, Location } from '@angular/common';
import { BehaviorSubject, of } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';

import { TeammateMessagingComponent } from './teammate-messaging.component';
import { WebSocketService } from '../services/web-socket.service';
import { MessagingStateService } from '../services/messaging-state.service';
import { DeviceStateService } from '../services/device-state.service';
import { UserService } from '../services/user.service';
import { UserTypeService } from '../services/user-type.service';
import { BriTeammateAuthorizationService } from '@davita/bridge-utility-library/authorization';
import { HttpTeammateMessagingService } from '../services/http-teammate-messaging.service';
import { MessagingProxyService } from '../services/messaging-proxy.service';
import { Thread } from '../models';
import { UserType } from '../models/roles';
import {
  MockWebSocketService,
  MockMessagingStateService,
  MockDeviceStateService
} from '../shared/mocks/services';

describe('TeammateMessagingComponent', () => {
  let component: TeammateMessagingComponent;
  let fixture: ComponentFixture<TeammateMessagingComponent>;

  // Mock services
  let mockRouter: jasmine.SpyObj<Router>;
  let mockLocation: jasmine.SpyObj<Location>;
  let mockWebSocketService: MockWebSocketService;
  let mockHttpTeammateMessagingService: jasmine.SpyObj<HttpTeammateMessagingService>;
  let mockMessagingStateService: MockMessagingStateService;
  let mockDeviceStateService: MockDeviceStateService;
  let mockUserService: jasmine.SpyObj<UserService>;
  let mockUserTypeService: jasmine.SpyObj<UserTypeService>;
  let mockBriTeammateAuthService: jasmine.SpyObj<BriTeammateAuthorizationService>;
  let mockMessagingProxyService: jasmine.SpyObj<MessagingProxyService>;
  let mockActivatedRoute: Partial<ActivatedRoute>;

  // Mock data
  const mockTeammateUser = {
    userLogin: 'testUser',
    firstName: 'Test',
    lastName: 'User'
  };

  const mockFacility = {
    id: '123',
    name: 'Test Facility'
  };

  // Mock threads data
  const mockThreadsData: Thread[] = [
    {
      id: '1',
      subject: 'Test Thread 1',
      lastMessage: 'Hello there',
      lastMessageSentAt: new Date().toISOString(),
      participants: [],
      unreadMessages: 0
    },
    {
      id: '2',
      subject: 'Test Thread 2',
      lastMessage: 'How are you?',
      lastMessageSentAt: new Date().toISOString(),
      participants: [],
      unreadMessages: 2
    }
  ];

  // Router events subject
  const routerEventsSubject = new BehaviorSubject<any>(null);

  beforeEach(async () => {
    // Create spies for all the services
    mockRouter = jasmine.createSpyObj('Router', ['navigate', 'url', 'events']);
    (mockRouter.url as any) = '/teammate-messaging/landing';
    (mockRouter.events as any) = routerEventsSubject.asObservable();

    mockLocation = jasmine.createSpyObj('Location', ['back']);

    mockWebSocketService = new MockWebSocketService();
    spyOn(mockWebSocketService, 'initWebSocket').and.callThrough();

    mockHttpTeammateMessagingService = jasmine.createSpyObj('HttpTeammateMessagingService',
      ['getUserThreads', 'updateThreadUnreadMessages']);
    mockHttpTeammateMessagingService.getUserThreads.and.returnValue(of(mockThreadsData));
    mockHttpTeammateMessagingService.updateThreadUnreadMessages.and.returnValue(of(0));

    mockMessagingStateService = new MockMessagingStateService();
    spyOn(mockMessagingStateService, 'updateThreads').and.callThrough();
    spyOn(mockMessagingStateService, 'updateThreadUnreadMessageCount').and.callThrough();
    // Add setUser method to the mock if needed
    (mockMessagingStateService as any).setUser = jasmine.createSpy('setUser');

    mockDeviceStateService = new MockDeviceStateService();
    spyOn(mockDeviceStateService, 'determineDevice').and.returnValue('desktop');

    mockUserService = jasmine.createSpyObj('UserService', ['createUserIfNotExists', 'getPatientName']);
    // Fix the return type to match what's expected
    mockUserService.createUserIfNotExists.and.returnValue(of({
      externalId: '123',
      firstName: 'Test',
      lastName: 'User',
      isPractitioner: false,
      fullName: 'Test User',
      email: '<EMAIL>',
      phone: '************',
      adUsername: 'testuser'
    } as any));
    mockUserService.getPatientName.and.returnValue('Test Patient');

    mockUserTypeService = jasmine.createSpyObj('UserTypeService', ['setUserType']);

    mockBriTeammateAuthService = jasmine.createSpyObj('BriTeammateAuthorizationService',
      ['user$', 'facility$']);
    // Set up the observables
    (mockBriTeammateAuthService.user$ as any) = of({
      oim_details: mockTeammateUser
    });
    (mockBriTeammateAuthService.facility$ as any) = of(mockFacility);

    mockMessagingProxyService = jasmine.createSpyObj('MessagingProxyService', ['setupHttpService']);

    mockActivatedRoute = {
      snapshot: {
        url: [],
        params: {},
        queryParams: {},
        fragment: null,
        data: {},
        outlet: 'primary',
        component: null,
        routeConfig: null,
        root: null as any,
        parent: null,
        firstChild: null,
        children: [],
        pathFromRoot: [],
        paramMap: {
          has: () => false,
          get: () => null,
          getAll: () => [],
          keys: []
        },
        queryParamMap: {
          has: () => false,
          get: () => null,
          getAll: () => [],
          keys: []
        }
      } as any
    };

    await TestBed.configureTestingModule({
      declarations: [TeammateMessagingComponent],
      providers: [
        DatePipe,
        { provide: Router, useValue: mockRouter },
        { provide: Location, useValue: mockLocation },
        { provide: WebSocketService, useValue: mockWebSocketService },
        { provide: HttpTeammateMessagingService, useValue: mockHttpTeammateMessagingService },
        { provide: MessagingStateService, useValue: mockMessagingStateService },
        { provide: DeviceStateService, useValue: mockDeviceStateService },
        { provide: UserService, useValue: mockUserService },
        { provide: UserTypeService, useValue: mockUserTypeService },
        { provide: BriTeammateAuthorizationService, useValue: mockBriTeammateAuthService },
        { provide: MessagingProxyService, useValue: mockMessagingProxyService },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(TeammateMessagingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });



  it('should set deviceType on init', () => {
    expect(mockDeviceStateService.determineDevice).toHaveBeenCalled();
    expect(component.deviceType).toBe('desktop');
  });

  it('should set user type to TEAMMATE', () => {
    expect(mockUserTypeService.setUserType).toHaveBeenCalled();
  });

  it('should initialize proxy service', () => {
    expect(mockMessagingProxyService.setupHttpService).toHaveBeenCalled();
  });

  it('should format date as time when date is today', () => {
    const today = new Date();
    const formattedDate = component.dateFormat(today.toISOString());
    expect(formattedDate).toMatch(/\d{1,2}:\d{2} [AP]M/); // Format like "3:30 PM"
  });

  it('should format date as MM/dd when date is not today', () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const formattedDate = component.dateFormat(yesterday.toISOString());
    expect(formattedDate).toMatch(/\d{2}\/\d{2}/); // Format like "05/21"
  });

  it('should navigate to new message when newMessage is called', () => {
    // Add inboxVisible property to the mock
    (mockDeviceStateService as any).inboxVisible = true;

    component.newMessage();
    expect((mockDeviceStateService as any).inboxVisible).toBe(false);
    expect(mockDeviceStateService.threadListVisible).toBe(false);
    expect(mockRouter.navigate).toHaveBeenCalledWith(['./new'], jasmine.any(Object));
  });

  it('should handle thread click', () => {
    const thread = mockThreadsData[1]; // Thread with unread messages
    component.threadClick(thread);
    expect(mockHttpTeammateMessagingService.updateThreadUnreadMessages).toHaveBeenCalledWith(thread.id);
    expect(mockDeviceStateService.threadListVisible).toBe(false);
    expect(mockRouter.navigate).toHaveBeenCalledWith([`./thread/${thread.id}`], jasmine.any(Object));
  });

  it('should go back to previous page when on landing page', () => {
    // Set the router url property
    Object.defineProperty(mockRouter, 'url', {
      get: () => '/teammate-messaging/landing'
    });

    component.goBack();
    expect(mockLocation.back).toHaveBeenCalled();
  });

  it('should navigate to landing when not on landing page', () => {
    // Set the router url property
    Object.defineProperty(mockRouter, 'url', {
      get: () => '/teammate-messaging/thread/123'
    });

    component.goBack();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['./landing'], jasmine.any(Object));
  });

  it('should update deviceType on window resize', () => {
    // Reset the spy
    (mockDeviceStateService.determineDevice as jasmine.Spy).calls.reset();

    component.onResize();
    expect(mockDeviceStateService.determineDevice).toHaveBeenCalled();
  });

  it('should get patient name from user service', () => {
    const thread = mockThreadsData[0];
    const result = component.getPatientName(thread);
    expect(mockUserService.getPatientName).toHaveBeenCalledWith(thread);
    expect(result).toBe('Test Patient');
  });
});
