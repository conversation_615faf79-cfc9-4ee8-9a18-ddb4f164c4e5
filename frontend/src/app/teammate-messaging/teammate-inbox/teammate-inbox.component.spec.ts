import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TeammateInboxComponent } from './teammate-inbox.component';
import { COMMON_TEST_IMPORTS, COMMON_TEST_PROVIDERS, COMMON_TEST_SCHEMAS } from 'src/app/testing/test-helpers';

describe('TeammateInboxComponent', () => {
  let component: TeammateInboxComponent;
  let fixture: ComponentFixture<TeammateInboxComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: COMMON_TEST_IMPORTS,
      declarations: [TeammateInboxComponent],
      providers: COMMON_TEST_PROVIDERS,
      schemas: COMMON_TEST_SCHEMAS
    });
    fixture = TestBed.createComponent(TeammateInboxComponent);
    component = fixture.componentInstance;
    // Don't call detectChanges() to avoid rendering the component
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
