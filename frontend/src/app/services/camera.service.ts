import { Injectable } from '@angular/core';
import { Camera, CameraResultType, CameraSource, Photo } from '@capacitor/camera';
import { Capacitor } from '@capacitor/core';

export interface CameraPhoto {
  webPath?: string;
  format: string;
  saved: boolean;
  file?: File;
}

@Injectable({
  providedIn: 'root'
})
export class CameraService {

  constructor() { }

  /**
   * Take a photo using the device camera
   */
  async takePhoto(): Promise<CameraPhoto | null> {
    try {
      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Camera,
      });

      return this.processPhoto(image);
    } catch (error) {
      console.error('Error taking photo:', error);
      return null;
    }
  }

  /**
   * Select a photo from the device gallery
   */
  async selectFromGallery(): Promise<CameraPhoto | null> {
    try {
      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Photos,
      });

      return this.processPhoto(image);
    } catch (error) {
      console.error('Error selecting from gallery:', error);
      return null;
    }
  }

  /**
   * Show action sheet to choose between camera and gallery
   */
  async selectPhoto(): Promise<CameraPhoto | null> {
    try {
      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Prompt,
        width: 1024, // Limit image size for better performance
        height: 1024,
      });

      return this.processPhoto(image);
    } catch (error) {
      console.error('Error selecting photo:', error);
      // Return null for graceful handling - user may have cancelled
      return null;
    }
  }

  /**
   * Check if the app is running on a native platform
   */
  isNativePlatform(): boolean {
    return Capacitor.isNativePlatform();
  }

  /**
   * Process the photo and convert to File object
   */
  private async processPhoto(photo: Photo): Promise<CameraPhoto> {
    const fileName = `photo_${new Date().getTime()}.${photo.format}`;

    // Convert data URL to File object
    const file = await this.dataUrlToFile(photo.dataUrl!, fileName);

    return {
      webPath: photo.webPath,
      format: photo.format || 'jpeg',
      saved: false,
      file: file
    };
  }

  /**
   * Convert data URL to File object
   */
  private async dataUrlToFile(dataUrl: string, fileName: string): Promise<File> {
    const response = await fetch(dataUrl);
    const blob = await response.blob();
    return new File([blob], fileName, { type: blob.type });
  }

  /**
   * Request camera permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const permissions = await Camera.requestPermissions();
      return permissions.camera === 'granted' && permissions.photos === 'granted';
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  }

  /**
   * Check camera permissions
   */
  async checkPermissions(): Promise<boolean> {
    try {
      const permissions = await Camera.checkPermissions();
      return permissions.camera === 'granted' && permissions.photos === 'granted';
    } catch (error) {
      console.error('Error checking permissions:', error);
      return false;
    }
  }
}
